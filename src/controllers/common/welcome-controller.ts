import { z } from 'zod';

import { sendResponse, createHandler } from '@/utils/handler';

const welcomeSchema = {
  response: {
    body: z.object({
      messages: z.string()
    })
  }
} as const;

export const handleWelcome = createHandler(welcomeSchema, async (req, res) => {
  // TypeScript ensures email exists because of our schema

  sendResponse(res, 'Welcome!!', {
    messages: `Welcome to the API!`
  });
});
