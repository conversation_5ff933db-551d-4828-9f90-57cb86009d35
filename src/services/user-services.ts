/**
 * @fileoverview User service
 * Handles all user-related database operations with Drizzle ORM
 */

import { eq, and } from 'drizzle-orm';
import argon2 from 'argon2';

import { db } from '@/db';
import { users, authProvider, type enumUsersRole } from '@/db/schemas';
import { BackendError } from '@/utils/errors';

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role?: (typeof enumUsersRole.enumValues)[number];
  tennantId?: number;
}

export interface UserWithAuth {
  userId: number;
  userGuid: string | null;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  role: (typeof enumUsersRole.enumValues)[number] | null;
  status: string | null;
  emailVerified: boolean | null;
  createdAt: string | null;
  updatedAt: string | null;
  tennantId: number | null;
  authProviders?: Array<{
    authId: number;
    jsonTokenId: string | null;
    refreshToken: string | null;
    isJtiValid: boolean | null;
  }>;
}

/**
 * Get user by email with optional auth provider data
 */
export async function getUserByEmail(
  email: string,
  includeAuth = false
): Promise<UserWithAuth | null> {
  try {
    const query = db
      .select({
        userId: users.userId,
        userGuid: users.userGuid,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        phone: users.phone,
        role: users.role,
        status: users.status,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        tennantId: users.tennantId,
        password: users.password
      })
      .from(users)
      .where(eq(users.email, email));

    const result = await query;
    const user = result[0];

    if (!user) {
      return null;
    }

    let authProviders;
    if (includeAuth) {
      const authData = await db
        .select({
          authId: authProvider.authId,
          jsonTokenId: authProvider.jsonTokenId,
          refreshToken: authProvider.refreshToken,
          isJtiValid: authProvider.isJtiValid
        })
        .from(authProvider)
        .where(eq(authProvider.userId, user.userId));

      authProviders = authData;
    }

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    return {
      ...userWithoutPassword,
      authProviders
    };
  } catch (error) {
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to fetch user by email',
      context: 'getUserByEmail',
      statusCode: 500,
      originalError: error
    });
  }
}

/**
 * Get user by ID with optional auth provider data
 */
export async function getUserById(
  userId: number,
  includeAuth = false
): Promise<UserWithAuth | null> {
  try {
    const query = db
      .select({
        userId: users.userId,
        userGuid: users.userGuid,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        phone: users.phone,
        role: users.role,
        status: users.status,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        tennantId: users.tennantId
      })
      .from(users)
      .where(eq(users.userId, userId));

    const result = await query;
    const user = result[0];

    if (!user) {
      return null;
    }

    let authProviders;
    if (includeAuth) {
      const authData = await db
        .select({
          authId: authProvider.authId,
          jsonTokenId: authProvider.jsonTokenId,
          refreshToken: authProvider.refreshToken,
          isJtiValid: authProvider.isJtiValid
        })
        .from(authProvider)
        .where(eq(authProvider.userId, user.userId));

      authProviders = authData;
    }

    return {
      ...user,
      authProviders
    };
  } catch (error) {
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to fetch user by ID',
      context: 'getUserById',
      statusCode: 500,
      originalError: error
    });
  }
}

/**
 * Create a new user with hashed password
 */
export async function createUser(
  userData: CreateUserData
): Promise<UserWithAuth> {
  try {
    // Check if user already exists
    const existingUser = await getUserByEmail(userData.email);
    if (existingUser) {
      throw new BackendError('CONFLICT', {
        message: 'User with this email already exists',
        context: 'createUser',
        statusCode: 409
      });
    }

    // Hash password
    const hashedPassword = await argon2.hash(userData.password);

    // Create user
    const newUser = await db
      .insert(users)
      .values({
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        role: userData.role || 'USER',
        status: 'ACTIVATION_PENDING',
        emailVerified: false,
        tennantId: userData.tennantId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
      .returning({
        userId: users.userId,
        userGuid: users.userGuid,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        phone: users.phone,
        role: users.role,
        status: users.status,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        tennantId: users.tennantId
      });

    return newUser[0];
  } catch (error) {
    if (error instanceof BackendError) {
      throw error;
    }
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to create user',
      context: 'createUser',
      statusCode: 500,
      originalError: error
    });
  }
}

/**
 * Update user email verification status
 */
export async function updateEmailVerification(
  userId: number,
  verified: boolean
): Promise<void> {
  try {
    await db
      .update(users)
      .set({
        emailVerified: verified,
        status: verified ? 'OFFLINE' : 'ACTIVATION_PENDING',
        updatedAt: new Date().toISOString()
      })
      .where(eq(users.userId, userId));
  } catch (error) {
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to update email verification',
      context: 'updateEmailVerification',
      statusCode: 500,
      originalError: error
    });
  }
}

/**
 * Verify user password
 */
export async function verifyUserPassword(
  email: string,
  password: string
): Promise<UserWithAuth | null> {
  try {
    const query = await db
      .select({
        userId: users.userId,
        userGuid: users.userGuid,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        phone: users.phone,
        role: users.role,
        status: users.status,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        tennantId: users.tennantId,
        password: users.password
      })
      .from(users)
      .where(eq(users.email, email));

    const user = query[0];
    if (!user || !user.password) {
      return null;
    }

    const isValidPassword = await argon2.verify(user.password, password);
    if (!isValidPassword) {
      return null;
    }

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to verify user password',
      context: 'verifyUserPassword',
      statusCode: 500,
      originalError: error
    });
  }
}

/**
 * Update user password
 */
export async function updateUserPassword(
  userId: number,
  newPassword: string
): Promise<void> {
  try {
    const hashedPassword = await argon2.hash(newPassword);

    await db
      .update(users)
      .set({
        password: hashedPassword,
        updatedAt: new Date().toISOString()
      })
      .where(eq(users.userId, userId));
  } catch (error) {
    throw new BackendError('DATABASE_ERROR', {
      message: 'Failed to update user password',
      context: 'updateUserPassword',
      statusCode: 500,
      originalError: error
    });
  }
}
